#!/usr/bin/env python3
"""
Fixed SAITS Model Implementation
===============================

This module provides a numerically stable SAITS implementation that fixes the "Input contains NaN" error.
Key improvements:
1. Comprehensive NaN detection and handling
2. Numerical stability improvements
3. Robust error handling
4. Input validation
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.preprocessing import StandardScaler
from sklearn.base import BaseEstimator, RegressorMixin
import warnings
warnings.filterwarnings('ignore')

def debug_tensor(tensor, name, step="", verbose=False):
    """Debug function to check tensor properties and NaN values."""
    if torch.is_tensor(tensor):
        has_nan = torch.isnan(tensor).any().item()
        has_inf = torch.isinf(tensor).any().item()
        if has_nan or has_inf or verbose:
            min_val = tensor.min().item() if tensor.numel() > 0 else 0
            max_val = tensor.max().item() if tensor.numel() > 0 else 0
            print(f"🔍 {step} {name}: shape={tensor.shape}, NaN={has_nan}, Inf={has_inf}, "
                  f"min={min_val:.6f}, max={max_val:.6f}")
            if has_nan or has_inf:
                return False
    return True

def safe_tensor_operation(tensor, operation_name="operation"):
    """Safely handle tensor operations with NaN/Inf checking."""
    if torch.is_tensor(tensor):
        if torch.isnan(tensor).any() or torch.isinf(tensor).any():
            print(f"⚠️  Cleaning {operation_name}: NaN/Inf detected")
            tensor = torch.nan_to_num(tensor, nan=0.0, posinf=1e6, neginf=-1e6)
        # Clamp to reasonable range
        tensor = torch.clamp(tensor, -1e6, 1e6)
    return tensor

class StablePositionalEncoding(nn.Module):
    """Numerically stable positional encoding."""
    
    def __init__(self, d_model, n_position=200):
        super().__init__()
        self.d_model = max(d_model, 1)  # Prevent division by zero
        
        # Create positional encoding with numerical stability
        pe = torch.zeros(n_position, self.d_model)
        position = torch.arange(0, n_position, dtype=torch.float).unsqueeze(1)
        
        # Prevent division by zero and use stable computation
        if self.d_model > 1:
            div_term = torch.exp(torch.arange(0, self.d_model, 2).float() * 
                               (-np.log(10000.0) / self.d_model))
            
            pe[:, 0::2] = torch.sin(position * div_term)
            if self.d_model > 1:
                pe[:, 1::2] = torch.cos(position * div_term[:pe[:, 1::2].shape[1]])
        else:
            # Handle edge case where d_model = 1
            pe[:, 0] = torch.sin(position.squeeze())
        
        # Clamp to prevent extreme values
        pe = torch.clamp(pe, -10.0, 10.0)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        debug_tensor(x, "PositionalEncoding input", "PE")
        
        # Clean input
        x = safe_tensor_operation(x, "PE input")
        
        seq_len = x.size(1)
        
        # Ensure we don't exceed buffer
        if seq_len > self.pe.size(0):
            print(f"⚠️  Sequence length {seq_len} exceeds PE buffer {self.pe.size(0)}")
            seq_len = min(seq_len, self.pe.size(0))
        
        pe_slice = self.pe[:seq_len, :].unsqueeze(0)
        output = x + pe_slice
        
        # Clean output
        output = safe_tensor_operation(output, "PE output")
        
        debug_tensor(output, "PositionalEncoding output", "PE")
        return output

class StableScaledDotProductAttention(nn.Module):
    """Numerically stable scaled dot-product attention."""
    
    def __init__(self, temperature, attn_dropout=0.1):
        super().__init__()
        self.temperature = max(temperature, 1e-6)  # Prevent division by zero
        self.dropout = nn.Dropout(attn_dropout)
    
    def forward(self, q, k, v, mask=None):
        # Clean inputs
        q = safe_tensor_operation(q, "attention q")
        k = safe_tensor_operation(k, "attention k") 
        v = safe_tensor_operation(v, "attention v")
        
        # Compute attention with numerical stability
        attn = torch.matmul(q / self.temperature, k.transpose(2, 3))
        attn = safe_tensor_operation(attn, "attention scores")
        
        # Clamp before softmax to prevent overflow
        attn = torch.clamp(attn, -50.0, 50.0)
        
        if mask is not None:
            mask = safe_tensor_operation(mask, "attention mask")
            attn = attn.masked_fill(mask == 0, -1e9)
        
        # Stable softmax
        attn = F.softmax(attn, dim=-1)
        attn = torch.clamp(attn, 1e-8, 1.0)  # Ensure valid probabilities
        attn = self.dropout(attn)
        
        output = torch.matmul(attn, v)
        output = safe_tensor_operation(output, "attention output")
        
        return output, attn

class StableMultiHeadAttention(nn.Module):
    """Numerically stable multi-head attention."""
    
    def __init__(self, d_model, n_head, d_k, d_v, dropout=0.1):
        super().__init__()
        self.n_head = n_head
        self.d_k = max(d_k, 1)
        self.d_v = max(d_v, 1)
        
        self.w_qs = nn.Linear(d_model, n_head * self.d_k, bias=False)
        self.w_ks = nn.Linear(d_model, n_head * self.d_k, bias=False)
        self.w_vs = nn.Linear(d_model, n_head * self.d_v, bias=False)
        self.fc = nn.Linear(n_head * self.d_v, d_model, bias=False)
        
        self.attention = StableScaledDotProductAttention(temperature=max(self.d_k ** 0.5, 1e-6))
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model, eps=1e-6)
        
        # Initialize weights properly
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights to prevent NaN."""
        for module in [self.w_qs, self.w_ks, self.w_vs, self.fc]:
            nn.init.xavier_uniform_(module.weight, gain=0.1)
    
    def forward(self, q, k, v, mask=None):
        debug_tensor(q, "MHA input", "MHA")
        
        # Clean inputs
        q = safe_tensor_operation(q, "MHA q")
        k = safe_tensor_operation(k, "MHA k")
        v = safe_tensor_operation(v, "MHA v")
        
        sz_b, len_q, len_k, len_v = q.size(0), q.size(1), k.size(1), v.size(1)
        residual = q.clone()
        
        # Linear transformations
        q = self.w_qs(q).view(sz_b, len_q, self.n_head, self.d_k)
        k = self.w_ks(k).view(sz_b, len_k, self.n_head, self.d_k)
        v = self.w_vs(v).view(sz_b, len_v, self.n_head, self.d_v)
        
        # Clean after linear transformations
        q = safe_tensor_operation(q, "MHA q after linear")
        k = safe_tensor_operation(k, "MHA k after linear")
        v = safe_tensor_operation(v, "MHA v after linear")
        
        # Transpose for attention
        q, k, v = q.transpose(1, 2), k.transpose(1, 2), v.transpose(1, 2)
        
        if mask is not None:
            mask = mask.unsqueeze(1).repeat(1, self.n_head, 1, 1)
        
        q, attn = self.attention(q, k, v, mask=mask)
        
        # Transpose back
        q = q.transpose(1, 2).contiguous().view(sz_b, len_q, -1)
        
        q = self.dropout(self.fc(q))
        q = safe_tensor_operation(q, "MHA after fc")
        
        q += residual
        
        # Safe layer normalization
        try:
            q = self.layer_norm(q)
            q = safe_tensor_operation(q, "MHA after layer_norm")
        except Exception as e:
            print(f"⚠️  LayerNorm failed: {e}, using manual normalization")
            mean = q.mean(dim=-1, keepdim=True)
            var = q.var(dim=-1, keepdim=True, unbiased=False)
            q = (q - mean) / torch.sqrt(var + 1e-6)
            q = safe_tensor_operation(q, "MHA after manual norm")
        
        debug_tensor(q, "MHA output", "MHA")
        return q, attn

class StablePositionwiseFeedForward(nn.Module):
    """Numerically stable position-wise feed forward network."""
    
    def __init__(self, d_model, d_inner, dropout=0.1):
        super().__init__()
        self.w_1 = nn.Linear(d_model, d_inner)
        self.w_2 = nn.Linear(d_inner, d_model)
        self.layer_norm = nn.LayerNorm(d_model, eps=1e-6)
        self.dropout = nn.Dropout(dropout)
        
        # Initialize weights
        nn.init.xavier_uniform_(self.w_1.weight, gain=0.1)
        nn.init.xavier_uniform_(self.w_2.weight, gain=0.1)
    
    def forward(self, x):
        residual = x.clone()
        
        # First linear transformation
        x = self.w_1(x)
        x = safe_tensor_operation(x, "FFN w1")
        
        # ReLU activation
        x = F.relu(x)
        x = self.dropout(x)
        
        # Second linear transformation
        x = self.w_2(x)
        x = safe_tensor_operation(x, "FFN w2")
        x = self.dropout(x)
        
        # Residual connection
        x += residual
        
        # Layer normalization
        try:
            x = self.layer_norm(x)
            x = safe_tensor_operation(x, "FFN layer_norm")
        except Exception as e:
            print(f"⚠️  FFN LayerNorm failed: {e}, using manual normalization")
            mean = x.mean(dim=-1, keepdim=True)
            var = x.var(dim=-1, keepdim=True, unbiased=False)
            x = (x - mean) / torch.sqrt(var + 1e-6)
            x = safe_tensor_operation(x, "FFN manual norm")
        
        return x

def validate_model_inputs(inputs):
    """Validate SAITS model inputs."""
    print("🔍 Validating model inputs...")
    
    required_keys = ["X", "missing_mask"]
    for key in required_keys:
        if key not in inputs:
            raise ValueError(f"Missing required input: {key}")
        
        tensor = inputs[key]
        if not torch.is_tensor(tensor):
            raise ValueError(f"Input {key} must be a tensor")
        
        if tensor.numel() == 0:
            raise ValueError(f"Input {key} is empty")
        
        # Check for NaN/Inf
        if torch.isnan(tensor).any():
            print(f"⚠️  NaN detected in {key}, cleaning...")
            inputs[key] = torch.nan_to_num(tensor, nan=0.0)
        
        if torch.isinf(tensor).any():
            print(f"⚠️  Inf detected in {key}, cleaning...")
            inputs[key] = torch.nan_to_num(tensor, posinf=1e6, neginf=-1e6)
        
        # Check for extreme values
        min_val, max_val = tensor.min().item(), tensor.max().item()
        if abs(min_val) > 1e6 or abs(max_val) > 1e6:
            print(f"⚠️  Extreme values in {key}: [{min_val:.2e}, {max_val:.2e}]")
            inputs[key] = torch.clamp(tensor, -1e6, 1e6)
    
    print("✅ Input validation completed")
    return inputs

class StableEncoderLayer(nn.Module):
    """Stable encoder layer with self-attention and feed forward."""

    def __init__(self, d_time, d_feature, d_model, d_inner, n_head, d_k, d_v, dropout=0.1, **kwargs):
        super().__init__()
        self.slf_attn = StableMultiHeadAttention(d_model, n_head, d_k, d_v, dropout=dropout)
        self.pos_ffn = StablePositionwiseFeedForward(d_model, d_inner, dropout=dropout)

    def forward(self, enc_input, slf_attn_mask=None):
        enc_output, enc_slf_attn = self.slf_attn(
            enc_input, enc_input, enc_input, mask=slf_attn_mask)
        enc_output = self.pos_ffn(enc_output)
        return enc_output, enc_slf_attn

def stable_masked_mae_cal(inputs, target, mask):
    """Calculate masked mean absolute error with numerical stability."""
    # Clean inputs
    inputs = safe_tensor_operation(inputs, "MAE inputs")
    target = safe_tensor_operation(target, "MAE target")
    mask = safe_tensor_operation(mask, "MAE mask")

    # Ensure mask is in valid range
    mask = torch.clamp(mask, 0.0, 1.0)

    numerator = torch.sum(torch.abs(inputs - target) * mask)
    denominator = torch.sum(mask) + 1e-9

    result = numerator / denominator
    result = safe_tensor_operation(result, "MAE result")

    return result

class SAITS(nn.Module):
    """Numerically stable SAITS model for time series imputation."""

    def __init__(self, n_groups=1, n_group_inner_layers=1, d_time=100, d_feature=4,
                 d_model=64, d_inner=128, n_head=4, d_k=16, d_v=16, dropout=0.1,
                 input_with_mask=True, param_sharing_strategy="inner_group",
                 MIT=False, device='cpu', **kwargs):
        super().__init__()

        # Validate parameters
        self.n_groups = max(n_groups, 1)
        self.n_group_inner_layers = max(n_group_inner_layers, 1)
        self.input_with_mask = input_with_mask
        self.param_sharing_strategy = param_sharing_strategy
        self.MIT = MIT
        self.device = device

        # Ensure valid dimensions
        d_model = max(d_model, 4)
        d_inner = max(d_inner, d_model)
        n_head = max(n_head, 1)
        d_k = max(d_k, 1)
        d_v = max(d_v, 1)

        actual_d_feature = d_feature * 2 if self.input_with_mask else d_feature

        print(f"🔧 Creating SAITS: d_model={d_model}, n_head={n_head}, d_k={d_k}, d_v={d_v}")

        # Embedding layers
        self.embedding_1 = nn.Linear(actual_d_feature, d_model)
        self.embedding_2 = nn.Linear(actual_d_feature, d_model)

        # Positional encoding
        self.position_enc = StablePositionalEncoding(d_model, d_time)
        self.dropout = nn.Dropout(dropout)

        # Encoder layers for first block
        if self.param_sharing_strategy == "between_group":
            self.layer_stack_for_first_block = nn.ModuleList([
                StableEncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout)
                for _ in range(self.n_groups)
            ])
        else:
            self.layer_stack_for_first_block = nn.ModuleList([
                StableEncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout)
                for _ in range(self.n_group_inner_layers)
            ])

        # Encoder layers for second block
        if self.param_sharing_strategy == "between_group":
            self.layer_stack_for_second_block = nn.ModuleList([
                StableEncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout)
                for _ in range(self.n_groups)
            ])
        else:
            self.layer_stack_for_second_block = nn.ModuleList([
                StableEncoderLayer(d_time, actual_d_feature, d_model, d_inner, n_head, d_k, d_v, dropout)
                for _ in range(self.n_group_inner_layers)
            ])

        # Output layers
        self.reduce_dim_z = nn.Linear(d_model, d_feature)
        self.reduce_dim_beta = nn.Linear(d_model, d_feature)
        self.reduce_dim_gamma = nn.Linear(d_feature, d_feature)

        # Combination layer
        self.attn_weight_projection = nn.Linear(d_time, d_feature)
        self.weight_combine = nn.Linear(d_feature + d_feature, d_feature)

        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        """Initialize model weights to prevent NaN."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight, gain=0.1)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)

    def impute(self, inputs):
        # Validate inputs first
        inputs = validate_model_inputs(inputs)

        X, masks = inputs["X"], inputs["missing_mask"]

        debug_tensor(X, "SAITS X input", "SAITS")
        debug_tensor(masks, "SAITS masks input", "SAITS")

        # First DMSA block
        input_X_for_first = torch.cat([X, masks], dim=2) if self.input_with_mask else X
        input_X_for_first = safe_tensor_operation(input_X_for_first, "first block input")

        input_X_for_first = self.embedding_1(input_X_for_first)
        input_X_for_first = safe_tensor_operation(input_X_for_first, "first embedding")

        enc_output = self.dropout(self.position_enc(input_X_for_first))
        enc_output = safe_tensor_operation(enc_output, "first pos_enc")

        if self.param_sharing_strategy == "between_group":
            for _ in range(self.n_groups):
                for encoder_layer in self.layer_stack_for_first_block:
                    enc_output, _ = encoder_layer(enc_output)
                    enc_output = safe_tensor_operation(enc_output, "first block layer")
        else:
            for encoder_layer in self.layer_stack_for_first_block:
                for _ in range(self.n_group_inner_layers):
                    enc_output, _ = encoder_layer(enc_output)
                    enc_output = safe_tensor_operation(enc_output, "first block layer")

        X_tilde_1 = self.reduce_dim_z(enc_output)
        X_tilde_1 = safe_tensor_operation(X_tilde_1, "X_tilde_1")

        # Second DMSA block
        input_X_for_second = torch.cat([X, masks], dim=2) if self.input_with_mask else X
        input_X_for_second = safe_tensor_operation(input_X_for_second, "second block input")

        input_X_for_second = self.embedding_2(input_X_for_second)
        input_X_for_second = safe_tensor_operation(input_X_for_second, "second embedding")

        enc_output = self.dropout(self.position_enc(input_X_for_second))
        enc_output = safe_tensor_operation(enc_output, "second pos_enc")

        if self.param_sharing_strategy == "between_group":
            for _ in range(self.n_groups):
                for encoder_layer in self.layer_stack_for_second_block:
                    enc_output, attn_weights = encoder_layer(enc_output)
                    enc_output = safe_tensor_operation(enc_output, "second block layer")
        else:
            for encoder_layer in self.layer_stack_for_second_block:
                for _ in range(self.n_group_inner_layers):
                    enc_output, attn_weights = encoder_layer(enc_output)
                    enc_output = safe_tensor_operation(enc_output, "second block layer")

        X_tilde_2 = self.reduce_dim_gamma(F.relu(self.reduce_dim_beta(enc_output)))
        X_tilde_2 = safe_tensor_operation(X_tilde_2, "X_tilde_2")

        # Attention-weighted combination
        if attn_weights is not None:
            attn_weights = safe_tensor_operation(attn_weights, "attention weights")
            attn_weights = attn_weights.squeeze(dim=1) if attn_weights.dim() > 3 else attn_weights

            if len(attn_weights.shape) == 4:
                attn_weights = torch.transpose(attn_weights, 1, 3)
                attn_weights = attn_weights.mean(dim=3)
                attn_weights = torch.transpose(attn_weights, 1, 2)

            # Project attention weights to match feature dimensions
            projected_attn_weights = self.attn_weight_projection(attn_weights)
            
            combining_weights = F.sigmoid(
                self.weight_combine(torch.cat([masks, projected_attn_weights], dim=2))
            )
        else:
            # Fallback if attention weights are None
            combining_weights = F.sigmoid(self.weight_combine(torch.cat([masks, masks], dim=2)))

        combining_weights = safe_tensor_operation(combining_weights, "combining weights")

        # Combine X_tilde_1 and X_tilde_2
        X_tilde_3 = (1 - combining_weights) * X_tilde_2 + combining_weights * X_tilde_1
        X_tilde_3 = safe_tensor_operation(X_tilde_3, "X_tilde_3")

        # Replace non-missing part with original data
        X_c = masks * X + (1 - masks) * X_tilde_3
        X_c = safe_tensor_operation(X_c, "X_c final")

        debug_tensor(X_c, "SAITS output", "SAITS")

        return X_c, [X_tilde_1, X_tilde_2, X_tilde_3]

    def forward(self, inputs, stage="train"):
        try:
            X, masks = inputs["X"], inputs["missing_mask"]
            reconstruction_loss = 0

            imputed_data, [X_tilde_1, X_tilde_2, X_tilde_3] = self.impute(inputs)

            reconstruction_loss += stable_masked_mae_cal(X_tilde_1, X, masks)
            reconstruction_loss += stable_masked_mae_cal(X_tilde_2, X, masks)
            final_reconstruction_MAE = stable_masked_mae_cal(X_tilde_3, X, masks)
            reconstruction_loss += final_reconstruction_MAE
            reconstruction_loss /= 3

            return {
                'loss': reconstruction_loss,
                'imputed_data': imputed_data,
                'reconstruction_MAE': final_reconstruction_MAE
            }
        except Exception as e:
            print(f"❌ SAITS forward pass failed: {e}")
            # To aid debugging, print input shapes
            if 'X' in inputs and 'missing_mask' in inputs:
                print(f"   Input shapes: X={inputs['X'].shape}")
                print(f"   Input shapes: mask={inputs['missing_mask'].shape}")
            # Return a placeholder loss to prevent crashing the training loop
            return {'loss': torch.tensor(float('inf')), 'imputed_data': inputs['X']}

class SAITSRegressor(BaseEstimator, RegressorMixin):
    """
    SAITS Regressor with a stable implementation.
    - Handles NaN/Inf values robustly
    - Provides detailed error logging
    - Includes GPU support detection and management
    """
    def __init__(self, sequence_length=50, n_groups=1, n_group_inner_layers=1,
                 d_model=128, d_inner=256, n_head=4, dropout=0.1,
                 epochs=100, batch_size=64, learning_rate=0.001, patience=10,
                 random_state=42, input_with_mask=True, param_sharing_strategy="inner_group",
                 MIT=False):
        
        # Core model hyperparameters
        self.sequence_length = sequence_length
        self.n_groups = n_groups
        self.n_group_inner_layers = n_group_inner_layers
        self.d_model = d_model
        self.d_inner = d_inner
        self.n_head = n_head
        self.dropout = dropout
        self.input_with_mask = input_with_mask
        self.param_sharing_strategy = param_sharing_strategy
        self.MIT = MIT

        # Training parameters
        self.epochs = epochs
        self.batch_size = batch_size
        self.learning_rate = learning_rate
        self.patience = patience
        
        # Other parameters
        self.random_state = random_state
        self.device = self._get_device()
        self.model = None
        self.scaler = StandardScaler()

    def _get_device(self):
        """Detect and return the best available device."""
        if torch.cuda.is_available():
            print("✅ CUDA is available. Using GPU.")
            return torch.device("cuda")
        else:
            print("⚠️ CUDA not available. Using CPU.")
            return torch.device("cpu")

    def _validate_and_clean_data(self, X, y=None, stage="fit"):
        """Validate and clean input data."""
        # Convert to DataFrame if numpy array
        if isinstance(X, np.ndarray):
            X = pd.DataFrame(X, columns=[f'feature_{i}' for i in range(X.shape[1])])
        if y is not None and isinstance(y, np.ndarray):
            y = pd.Series(y, name='target')

        # Check for catastrophic NaN/Inf issues
        if X.isnull().all().all():
            raise ValueError("Input X is completely empty.")
        
        # Handle remaining NaNs by filling with mean
        if X.isnull().values.any():
            print(f"⚠️  NaNs detected in {stage} data. Filling with column means.")
            X = X.fillna(X.mean())
            
        return X, y

    def _prepare_data_stable(self, X, y=None, fit_scalers=False):
        """Prepare data with improved stability and NaN handling."""
        X, y = self._validate_and_clean_data(X, y)

        # Scale features
        if fit_scalers:
            self.scaler.fit(X)
        
        # Handle cases where scaler was not fitted
        if not hasattr(self.scaler, 'mean_'):
            print("⚠️  Scaler not fitted. Fitting now.")
            self.scaler.fit(X)
            
        X_scaled = self.scaler.transform(X)
        
        # Create sequences
        X_seq, y_seq, missing_mask, indicating_mask = self._create_sequences_stable(X_scaled, y, original_X=X)
        
        # Convert to tensors
        X_tensor = torch.tensor(X_seq, dtype=torch.float32).to(self.device)
        y_tensor = torch.tensor(y_seq, dtype=torch.float32).to(self.device)
        missing_mask_tensor = torch.tensor(missing_mask, dtype=torch.float32).to(self.device)
        indicating_mask_tensor = torch.tensor(indicating_mask, dtype=torch.float32).to(self.device)

        return {
            "X": X_tensor,
            "y": y_tensor,
            "missing_mask": missing_mask_tensor,
            "indicating_mask": indicating_mask_tensor,
            "n_features": X.shape[1]
        }

    def _create_sequences_stable(self, X, y=None, original_X=None, original_y=None):
        """Create sequences with robust NaN and mask handling."""
        n_samples, n_features = X.shape
        X_seq, y_seq, missing_mask, indicating_mask = [], [], [], []

        # Use original data to create masks, ensuring consistency
        original_data_missing = original_X.isnull().values if original_X is not None else np.isnan(X)

        for i in range(n_samples - self.sequence_length + 1):
            # Sequence data
            X_sequence = X[i:i + self.sequence_length]
            
            # Missing mask (0 for missing, 1 for observed)
            miss_mask = 1 - original_data_missing[i:i + self.sequence_length]

            # Impute NaNs in sequence data with zeros (or other strategy)
            X_sequence = np.nan_to_num(X_sequence, nan=0.0)

            X_seq.append(X_sequence)
            missing_mask.append(miss_mask)

            # Target value
            if y is not None:
                y_seq.append(y.iloc[i + self.sequence_length - 1])

        X_seq = np.array(X_seq)
        y_seq = np.array(y_seq) if y is not None else None
        missing_mask = np.array(missing_mask)
        
        # Indicating mask is just the missing mask
        indicating_mask = missing_mask.copy()

        return X_seq, y_seq, missing_mask, indicating_mask

    def fit(self, X, y, eval_set=None, verbose=False):
        """Fit the stable SAITS model."""
        # Prepare training data
        train_data = self._prepare_data_stable(X, y, fit_scalers=True)
        d_feature = train_data['n_features']
        d_time = self.sequence_length

        # Initialize the model
        self.model = SAITS(
            d_time=d_time,
            d_feature=d_feature,
            n_groups=self.n_groups,
            n_group_inner_layers=self.n_group_inner_layers,
            d_model=self.d_model,
            d_inner=self.d_inner,
            n_head=self.n_head,
            dropout=self.dropout,
            input_with_mask=self.input_with_mask,
            param_sharing_strategy=self.param_sharing_strategy,
            MIT=self.MIT,
            device=self.device
        ).to(self.device)

        optimizer = torch.optim.Adam(self.model.parameters(), lr=self.learning_rate)
        
        # Prepare validation data if provided
        if eval_set:
            X_val, y_val = eval_set[0]
            val_data = self._prepare_data_stable(X_val, y_val)
            val_loader = torch.utils.data.DataLoader(
                torch.utils.data.TensorDataset(val_data['X'], val_data['missing_mask']),
                batch_size=self.batch_size
            )

        # Create data loader
        dataset = torch.utils.data.TensorDataset(train_data['X'], train_data['missing_mask'])
        loader = torch.utils.data.DataLoader(dataset, batch_size=self.batch_size, shuffle=True)

        best_val_loss = float('inf')
        patience_counter = 0

        print(f"🚀 Starting SAITS training for {self.epochs} epochs...")
        for epoch in range(self.epochs):
            self.model.train()
            epoch_loss = 0
            
            for i, (batch_X, batch_mask) in enumerate(loader):
                optimizer.zero_grad()
                
                inputs = {"X": batch_X, "missing_mask": batch_mask}
                
                # Forward pass
                output = self.model(inputs, stage="train")
                
                # Check for failed forward pass
                if torch.isinf(output['loss']):
                    print(f"❌ Batch {i} failed at epoch {epoch}: {output.get('error_message', 'Unknown error')}")
                    continue

                loss = output['loss']
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0) # Gradient clipping
                optimizer.step()
                
                epoch_loss += loss.item()

            avg_epoch_loss = epoch_loss / len(loader) if len(loader) > 0 else 0

            # Validation
            if eval_set:
                self.model.eval()
                val_loss = 0
                with torch.no_grad():
                    for batch_X_val, batch_mask_val in val_loader:
                        inputs_val = {"X": batch_X_val, "missing_mask": batch_mask_val}
                        output_val = self.model(inputs_val, stage="val")
                        val_loss += output_val['loss'].item()
                
                avg_val_loss = val_loss / len(val_loader) if len(val_loader) > 0 else 0

                if verbose:
                    print(f"Epoch {epoch+1}/{self.epochs}, Train Loss: {avg_epoch_loss:.6f}, Val Loss: {avg_val_loss:.6f}")

                if avg_val_loss < best_val_loss:
                    best_val_loss = avg_val_loss
                    patience_counter = 0
                    # Save best model state
                    torch.save(self.model.state_dict(), 'best_saits_model.pth')
                else:
                    patience_counter += 1
                    if patience_counter >= self.patience:
                        print(f"🛑 Early stopping at epoch {epoch+1}")
                        break
            elif verbose:
                print(f"Epoch {epoch+1}/{self.epochs}, Train Loss: {avg_epoch_loss:.6f}")
        
        # Load best model if early stopping was used
        if eval_set and 'best_saits_model.pth' in locals():
            self.model.load_state_dict(torch.load('best_saits_model.pth'))
            print("✅ Loaded best model state from early stopping.")

        return self

    def predict(self, X):
        """Predict using the trained stable SAITS model."""
        if self.model is None:
            raise RuntimeError("Model has not been fitted yet.")

        # Prepare prediction data
        pred_data = self._prepare_data_stable(X)
        X_seq = pred_data['X']
        
        self.model.eval()
        predictions = []

        with torch.no_grad():
            for i in range(0, len(X_seq), self.batch_size):
                batch_data = X_seq[i:i + self.batch_size]
                
                # Create inputs dictionary for this batch
                inputs = {
                    "X": batch_data,
                    "missing_mask": pred_data['missing_mask'][i:i + self.batch_size]
                }

                try:
                    imputed_batch, _ = self.model.impute(inputs)
                    
                    # Extract the last time step for each sequence in the batch
                    last_step_imputed = imputed_batch[:, -1, :]
                    predictions.append(last_step_imputed.cpu().numpy())
                except Exception as e:
                    print(f"⚠️  Prediction failed for a batch: {e}. Skipping.")
                    # Add NaNs for the failed batch
                    num_failed = len(batch_data)
                    predictions.append(np.full((num_failed, pred_data['n_features']), np.nan))

        if not predictions:
            return np.array([])
            
        # Inverse transform to get original scale
        try:
            # Concatenate all batch predictions
            full_predictions = np.concatenate(predictions, axis=0)

            # Inverse transform the entire predictions array
            inversed_predictions = self.scaler.inverse_transform(full_predictions)
            
            # Return the last column (target)
            return inversed_predictions[:, -1]
        except Exception as e:
            print(f"⚠️  Error during inverse transform: {e}")
            return np.array([])

# Example usage and testing
def test_stable_saits():
    """Test the stable SAITS implementation."""
    print("\n--- Running Stable SAITS Test ---")
    
    # Generate synthetic data with NaNs
    n_samples, n_features = 200, 5
    X = np.random.rand(n_samples, n_features) * 10
    y = X[:, -1] + np.random.randn(n_samples) * 0.5
    
    # Introduce NaNs
    nan_mask = np.random.rand(n_samples, n_features) < 0.2
    X[nan_mask] = np.nan
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

    try:
        # Initialize and fit the model
        model = SAITSRegressor(
            sequence_length=20,
            epochs=10,
            batch_size=16,
            d_model=32,
            d_inner=64,
            n_head=4,
            verbose=True
        )
        
        model.fit(X_train, y_train, eval_set=[(X_test, y_test)])
        
        # Make predictions
        predictions = model.predict(X_test)
        
        # Evaluate
        valid_preds = ~np.isnan(predictions)
        if np.any(valid_preds):
            mae = mean_absolute_error(y_test[valid_preds], predictions[valid_preds])
            print(f"\n✅ Test finished. MAE: {mae:.4f}")
        else:
            print("❌ Test failed: All predictions are NaN.")

    except Exception as e:
        print(f"❌ Test failed with error: {e}")

if __name__ == "__main__":
    test_stable_saits()
