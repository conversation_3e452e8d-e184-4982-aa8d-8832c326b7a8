{"meta": {"test_sets": [], "test_metrics": [], "learn_metrics": [{"best_value": "Min", "name": "RMSE"}], "launch_mode": "Train", "parameters": "", "iteration_count": 50, "learn_sets": ["learn"], "name": "experiment"}, "iterations": [{"learn": [11.0978298], "iteration": 0, "passed_time": 0.1221780827, "remaining_time": 5.986726053}, {"learn": [9.0713535], "iteration": 1, "passed_time": 0.1238390166, "remaining_time": 2.9721364}, {"learn": [7.970695722], "iteration": 2, "passed_time": 0.1252469459, "remaining_time": 1.962202153}, {"learn": [7.101674427], "iteration": 3, "passed_time": 0.1279557581, "remaining_time": 1.471491218}, {"learn": [6.487851552], "iteration": 4, "passed_time": 0.1306615932, "remaining_time": 1.175954338}, {"learn": [5.943562881], "iteration": 5, "passed_time": 0.133154085, "remaining_time": 0.9764632903}, {"learn": [5.69591987], "iteration": 6, "passed_time": 0.1346819093, "remaining_time": 0.8273317283}, {"learn": [5.483948835], "iteration": 7, "passed_time": 0.1359273763, "remaining_time": 0.7136187254}, {"learn": [5.273404994], "iteration": 8, "passed_time": 0.1370055444, "remaining_time": 0.624136369}, {"learn": [5.143639661], "iteration": 9, "passed_time": 0.1379112747, "remaining_time": 0.5516450987}, {"learn": [5.005746976], "iteration": 10, "passed_time": 0.1393534363, "remaining_time": 0.4940712743}, {"learn": [4.930928263], "iteration": 11, "passed_time": 0.1406145388, "remaining_time": 0.4452793729}, {"learn": [4.844899587], "iteration": 12, "passed_time": 0.1417059926, "remaining_time": 0.4033170559}, {"learn": [4.761077879], "iteration": 13, "passed_time": 0.1428468572, "remaining_time": 0.36732049}, {"learn": [4.67314497], "iteration": 14, "passed_time": 0.1442012321, "remaining_time": 0.3364695415}, {"learn": [4.594754723], "iteration": 15, "passed_time": 0.1455301705, "remaining_time": 0.3092516122}, {"learn": [4.525435939], "iteration": 16, "passed_time": 0.1466793912, "remaining_time": 0.2847305829}, {"learn": [4.491203236], "iteration": 17, "passed_time": 0.1477609466, "remaining_time": 0.2626861274}, {"learn": [4.439733008], "iteration": 18, "passed_time": 0.1488278781, "remaining_time": 0.2428244327}, {"learn": [4.383136638], "iteration": 19, "passed_time": 0.1507033574, "remaining_time": 0.2260550361}, {"learn": [4.29334443], "iteration": 20, "passed_time": 0.1527576097, "remaining_time": 0.2109509849}, {"learn": [4.173574938], "iteration": 21, "passed_time": 0.1544198852, "remaining_time": 0.1965343994}, {"learn": [4.136818933], "iteration": 22, "passed_time": 0.1557966894, "remaining_time": 0.1828917658}, {"learn": [4.090255373], "iteration": 23, "passed_time": 0.1568980756, "remaining_time": 0.1699729152}, {"learn": [4.046033412], "iteration": 24, "passed_time": 0.1583150856, "remaining_time": 0.1583150856}, {"learn": [4.033780985], "iteration": 25, "passed_time": 0.1595610329, "remaining_time": 0.1472871073}, {"learn": [3.960752358], "iteration": 26, "passed_time": 0.1606284827, "remaining_time": 0.1368316704}, {"learn": [3.834366783], "iteration": 27, "passed_time": 0.1618724592, "remaining_time": 0.1271855036}, {"learn": [3.764090102], "iteration": 28, "passed_time": 0.1629253639, "remaining_time": 0.1179804359}, {"learn": [3.693697835], "iteration": 29, "passed_time": 0.1639331424, "remaining_time": 0.1092887616}, {"learn": [3.614342569], "iteration": 30, "passed_time": 0.165150554, "remaining_time": 0.1012213073}, {"learn": [3.567321625], "iteration": 31, "passed_time": 0.1666471037, "remaining_time": 0.09373899582}, {"learn": [3.527284725], "iteration": 32, "passed_time": 0.1680332253, "remaining_time": 0.08656257062}, {"learn": [3.507197654], "iteration": 33, "passed_time": 0.1695505035, "remaining_time": 0.07978847222}, {"learn": [3.447257474], "iteration": 34, "passed_time": 0.1709702406, "remaining_time": 0.07327296027}, {"learn": [3.404105983], "iteration": 35, "passed_time": 0.1722710146, "remaining_time": 0.06699428346}, {"learn": [3.376494297], "iteration": 36, "passed_time": 0.17328738, "remaining_time": 0.06088475513}, {"learn": [3.340897709], "iteration": 37, "passed_time": 0.1744199161, "remaining_time": 0.05507997352}, {"learn": [3.319493723], "iteration": 38, "passed_time": 0.1758608392, "remaining_time": 0.04960177516}, {"learn": [3.29985471], "iteration": 39, "passed_time": 0.1770377521, "remaining_time": 0.04425943802}, {"learn": [3.266140453], "iteration": 40, "passed_time": 0.1782020887, "remaining_time": 0.03911753167}, {"learn": [3.206743346], "iteration": 41, "passed_time": 0.1795012291, "remaining_time": 0.0341907103}, {"learn": [3.182202395], "iteration": 42, "passed_time": 0.1808645585, "remaining_time": 0.02944306767}, {"learn": [3.126157265], "iteration": 43, "passed_time": 0.1819786147, "remaining_time": 0.02481526564}, {"learn": [3.075238649], "iteration": 44, "passed_time": 0.1832009714, "remaining_time": 0.02035566349}, {"learn": [3.014419424], "iteration": 45, "passed_time": 0.1846474113, "remaining_time": 0.01605629663}, {"learn": [2.964537927], "iteration": 46, "passed_time": 0.1861951035, "remaining_time": 0.01188479384}, {"learn": [2.929791014], "iteration": 47, "passed_time": 0.1874621213, "remaining_time": 0.007810921722}, {"learn": [2.908092109], "iteration": 48, "passed_time": 0.1886314071, "remaining_time": 0.003849620554}, {"learn": [2.885646685], "iteration": 49, "passed_time": 0.1899315241, "remaining_time": 0}]}