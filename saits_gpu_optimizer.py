"""
GPU optimization utilities for existing SAITS implementation.
"""

import torch
import torch.nn as nn
import numpy as np
from contextlib import contextmanager


class SAITSGPUOptimizer:
    """Utility class to optimize existing SAITS models for GPU acceleration."""
    
    def __init__(self, use_amp=True, compile_model=True, optimize_memory=True):
        self.use_amp = use_amp and torch.cuda.is_available()
        self.compile_model = compile_model and hasattr(torch, 'compile')
        self.optimize_memory = optimize_memory
        self.amp_scaler = torch.cuda.amp.GradScaler() if self.use_amp else None
        
    def optimize_model(self, model):
        """Apply optimizations to an existing SAITS model."""
        print("🔧 Applying GPU optimizations...")
        
        # Move to optimal GPU
        device = self._get_optimal_device()
        model = model.to(device)
        print(f"   ✅ Moved to device: {device}")
        
        # Compile model if available
        if self.compile_model:
            try:
                # Check if triton is available before compiling
                try:
                    import triton
                    model = torch.compile(model, mode='reduce-overhead')
                    print("   ✅ Model compiled for optimization")
                except ImportError:
                    print("   ⚠️  Triton not available, skipping torch.compile()")
                    print("   💡 Install triton for additional GPU optimization: pip install triton")
                except Exception as compile_error:
                    print(f"   ⚠️  Model compilation failed: {compile_error}")
                    print("   💡 Continuing without torch.compile() optimization")
            except Exception as e:
                print(f"   ⚠️  Model compilation setup failed: {e}")
        
        # Memory optimization
        if self.optimize_memory and torch.cuda.is_available():
            torch.cuda.empty_cache()
            print("   ✅ GPU memory cache cleared")
        
        return model
    
    def _get_optimal_device(self):
        """Get the optimal device for computation."""
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            if gpu_count > 1:
                # Find GPU with most free memory
                max_memory = 0
                best_gpu = 0
                for i in range(gpu_count):
                    props = torch.cuda.get_device_properties(i)
                    free_memory = props.total_memory - torch.cuda.memory_allocated(i)
                    if free_memory > max_memory:
                        max_memory = free_memory
                        best_gpu = i
                return torch.device(f'cuda:{best_gpu}')
            else:
                return torch.device('cuda:0')
        return torch.device('cpu')
    
    @contextmanager
    def mixed_precision_context(self):
        """Context manager for mixed precision training."""
        if self.use_amp:
            with torch.cuda.amp.autocast():
                yield
        else:
            yield
    
    def optimize_batch_processing(self, data_tensor, batch_size=None, num_workers=4):
        """Create optimized DataLoader for batch processing."""
        if batch_size is None:
            # Auto-determine optimal batch size based on GPU memory
            batch_size = self._get_optimal_batch_size(data_tensor)
        
        dataset = torch.utils.data.TensorDataset(data_tensor)
        
        return torch.utils.data.DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers if torch.cuda.is_available() else 0,
            pin_memory=True if torch.cuda.is_available() else False,
            persistent_workers=True if num_workers > 0 else False
        )
    
    def _get_optimal_batch_size(self, data_tensor):
        """Automatically determine optimal batch size based on available GPU memory."""
        if not torch.cuda.is_available():
            return 32
        
        # Get available GPU memory
        device = torch.cuda.current_device()
        total_memory = torch.cuda.get_device_properties(device).total_memory
        allocated_memory = torch.cuda.memory_allocated(device)
        free_memory = total_memory - allocated_memory
        
        # Estimate memory per sample (rough approximation)
        sample_size = data_tensor.element_size() * data_tensor[0].numel()
        
        # Use 70% of free memory for batch processing
        usable_memory = free_memory * 0.7
        optimal_batch_size = int(usable_memory / (sample_size * 10))  # Factor of 10 for model overhead
        
        # Clamp to reasonable range
        optimal_batch_size = max(8, min(optimal_batch_size, 256))
        
        print(f"   📊 Auto-determined batch size: {optimal_batch_size}")
        return optimal_batch_size
    
    def optimize_hyperparameters_for_gpu(self, base_params, model_type=None):
        """Suggest GPU-optimized hyperparameters."""
        optimized_params = base_params.copy()

        if torch.cuda.is_available():
            # Increase batch size for better GPU utilization
            optimized_params['batch_size'] = max(optimized_params.get('batch_size', 32), 64)

            # Increase model dimensions for better GPU utilization
            optimized_params['d_model'] = max(optimized_params.get('d_model', 64), 128)

            # Only add d_inner for models that support it (not SimpleSAITSRegressor)
            # SimpleSAITSRegressor calculates d_inner internally as d_model * 2
            if model_type != 'simple_saits' and 'd_inner' in base_params:
                optimized_params['d_inner'] = max(optimized_params.get('d_inner', 128), 256)
            elif model_type != 'simple_saits':
                # For full SAITS models, add d_inner if not present
                optimized_params['d_inner'] = max(optimized_params.get('d_inner', 128), 256)

            # Increase sequence length if memory allows
            if optimized_params.get('sequence_length', 50) < 100:
                optimized_params['sequence_length'] = min(100, optimized_params.get('sequence_length', 50) * 2)

            print("🎯 GPU-optimized hyperparameters:")
            for key, value in optimized_params.items():
                if key in base_params and base_params[key] != value:
                    print(f"   {key}: {base_params[key]} → {value}")

        return optimized_params


def patch_existing_saits_for_gpu():
    """Patch existing SAITS classes to add GPU optimizations."""
    
    # Import existing SAITS classes
    try:
        from saits_model import SAITSRegressor
        from simple_saits import SimpleSAITSRegressor
        
        # Add GPU optimization methods to existing classes
        def optimized_fit(self, X, y, eval_set=None, verbose=False, use_gpu_optimization=True):
            """Enhanced fit method with GPU optimizations."""
            if use_gpu_optimization:
                optimizer = SAITSGPUOptimizer()

                # Optimize hyperparameters based on data size
                original_batch_size = self.batch_size
                original_epochs = self.epochs

                if torch.cuda.is_available():
                    # Calculate maximum reasonable batch size based on data size
                    n_samples = len(X)
                    sequence_length = getattr(self, 'sequence_length', 50)

                    # Maximum sequences we can create
                    max_sequences = max(1, n_samples - sequence_length + 1)

                    # Don't exceed 1/4 of available sequences for batch size
                    max_reasonable_batch = min(64, max_sequences // 4)

                    # Only increase if it makes sense
                    if max_reasonable_batch > self.batch_size:
                        self.batch_size = max_reasonable_batch
                        print(f"🚀 Optimized batch size: {original_batch_size} → {self.batch_size} (max sequences: {max_sequences})")
                    else:
                        print(f"📊 Keeping batch size: {self.batch_size} (data size: {n_samples}, max sequences: {max_sequences})")

                # Call original fit method
                result = self._original_fit(X, y, eval_set, verbose)

                # Optimize the trained model
                if self.model is not None:
                    self.model = optimizer.optimize_model(self.model)

                return result
            else:
                return self._original_fit(X, y, eval_set, verbose)
        
        # Patch the classes
        if 'SAITSRegressor' in locals():
            SAITSRegressor._original_fit = SAITSRegressor.fit
            SAITSRegressor.fit = optimized_fit
            print("✅ Patched SAITSRegressor with GPU optimizations")
        
        if 'SimpleSAITSRegressor' in locals():
            SimpleSAITSRegressor._original_fit = SimpleSAITSRegressor.fit
            SimpleSAITSRegressor.fit = optimized_fit
            print("✅ Patched SimpleSAITSRegressor with GPU optimizations")
            
    except ImportError as e:
        print(f"⚠️  Could not patch SAITS classes: {e}")


def get_gpu_optimization_recommendations():
    """Get specific recommendations for optimizing SAITS on current hardware."""
    print("=" * 60)
    print(" GPU OPTIMIZATION RECOMMENDATIONS FOR SAITS")
    print("=" * 60)
    
    recommendations = []
    
    # Check GPU availability
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        for i in range(gpu_count):
            props = torch.cuda.get_device_properties(i)
            memory_gb = props.total_memory / 1024**3
            
            print(f"\n🎯 GPU {i}: {props.name} ({memory_gb:.1f} GB)")
            
            # Memory-based recommendations
            if memory_gb >= 24:
                recommendations.extend([
                    f"GPU {i}: Use large batch sizes (128-256)",
                    f"GPU {i}: Increase model dimensions (d_model=256, d_inner=512)",
                    f"GPU {i}: Use longer sequences (sequence_length=100-200)"
                ])
            elif memory_gb >= 12:
                recommendations.extend([
                    f"GPU {i}: Use medium batch sizes (64-128)",
                    f"GPU {i}: Standard model dimensions (d_model=128, d_inner=256)",
                    f"GPU {i}: Medium sequences (sequence_length=75-100)"
                ])
            elif memory_gb >= 6:
                recommendations.extend([
                    f"GPU {i}: Use smaller batch sizes (32-64)",
                    f"GPU {i}: Reduce model dimensions if needed",
                    f"GPU {i}: Standard sequences (sequence_length=50-75)"
                ])
            else:
                recommendations.extend([
                    f"GPU {i}: Use small batch sizes (16-32)",
                    f"GPU {i}: Consider using CPU for large models",
                    f"GPU {i}: Enable gradient accumulation"
                ])
    else:
        recommendations.append("Install CUDA-enabled PyTorch for GPU acceleration")
    
    # General optimization recommendations
    recommendations.extend([
        "Enable Automatic Mixed Precision (AMP) to save memory",
        "Use torch.compile() for additional speedup (PyTorch 2.0+)",
        "Increase num_workers in DataLoader for faster data loading",
        "Use gradient accumulation for effective larger batch sizes",
        "Enable pin_memory=True in DataLoader for GPU training"
    ])
    
    print("\n📋 Specific Recommendations:")
    for i, rec in enumerate(recommendations, 1):
        print(f"{i:2d}. {rec}")
    
    return recommendations


if __name__ == "__main__":
    # Run GPU optimization analysis
    get_gpu_optimization_recommendations()
    
    # Patch existing SAITS classes
    patch_existing_saits_for_gpu()
    
    print("\n" + "=" * 60)
    print(" USAGE EXAMPLE")
    print("=" * 60)
    print("""
# To use GPU optimizations with existing SAITS:

from saits_gpu_optimizer import SAITSGPUOptimizer, patch_existing_saits_for_gpu

# Patch existing classes
patch_existing_saits_for_gpu()

# Use your existing SAITS code with GPU optimizations
from saits_model import SAITSRegressor

model = SAITSRegressor(
    sequence_length=100,  # Increased for better GPU utilization
    batch_size=64,        # Increased for better GPU utilization
    d_model=128,          # Increased for better GPU utilization
    epochs=100
)

# Fit with GPU optimizations enabled (default)
model.fit(X_train, y_train, use_gpu_optimization=True)

# Or manually optimize
optimizer = SAITSGPUOptimizer()
optimized_params = optimizer.optimize_hyperparameters_for_gpu({
    'batch_size': 32,
    'd_model': 64,
    'sequence_length': 50
})
print(optimized_params)
""")
